'use client';

import Image from 'next/image';
import { motion } from 'framer-motion';

interface ProductCardProps {
  id: string;
  imageUrl: string;
  caption: string;
  name?: string;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  isAdmin?: boolean;
}

export default function ProductCard({
  id,
  imageUrl,
  caption,
  name,
  onEdit,
  onDelete,
  isAdmin = false
}: ProductCardProps) {
  return (
    <motion.div
      className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, amount: 0.3 }}
      transition={{ duration: 0.5 }}
      whileHover={{
        y: -5,
        transition: { duration: 0.2 }
      }}
    >
      {/* Image Container */}
      <div className="relative aspect-square overflow-hidden">
        <motion.div
          whileHover={{ scale: 1.05 }}
          transition={{ duration: 0.3 }}
          className="w-full h-full"
        >
          <Image
            src={imageUrl}
            alt={caption}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        </motion.div>
      </div>

      {/* Content */}
      <div className="p-4">
        {name && (
          <h3 className="font-semibold text-gray-900 mb-2 text-lg">{name}</h3>
        )}
        <p className="text-gray-700 text-sm leading-relaxed">{caption}</p>
        
        {/* Admin Controls */}
        {isAdmin && (onEdit || onDelete) && (
          <div className="mt-4 flex gap-2">
            {onEdit && (
              <motion.button
                onClick={() => onEdit(id)}
                className="px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Edit
              </motion.button>
            )}
            {onDelete && (
              <motion.button
                onClick={() => onDelete(id)}
                className="px-3 py-1 bg-red-500 text-white text-xs rounded hover:bg-red-600 transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Delete
              </motion.button>
            )}
          </div>
        )}
      </div>
    </motion.div>
  );
}

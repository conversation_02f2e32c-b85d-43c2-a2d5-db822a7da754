'use client';

import Image from 'next/image';
import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';

interface ProductCardProps {
  id: string;
  imageUrl: string;
  caption: string;
  name?: string;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  isAdmin?: boolean;
}

export default function ProductCard({
  id,
  imageUrl,
  caption,
  name,
  onEdit,
  onDelete,
  isAdmin = false
}: ProductCardProps) {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return (
    <motion.div
      className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, amount: 0.3 }}
      transition={{ duration: 0.5 }}
      whileHover={!isMobile ? {
        y: -5,
        transition: { duration: 0.2 }
      } : {}}
    >
      {/* Image Container */}
      <div className="relative w-full h-0 pb-[100%] overflow-hidden bg-gray-100">
        {imageLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-10">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        )}
        {imageError ? (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-10">
            <div className="text-center text-gray-500">
              <div className="text-2xl mb-2">📷</div>
              <div className="text-sm">Image not available</div>
            </div>
          </div>
        ) : (
          <motion.div
            whileHover={!isMobile ? { scale: 1.05 } : {}}
            transition={{ duration: 0.3 }}
            className="absolute inset-0 w-full h-full"
          >
            <Image
              src={imageUrl}
              alt={caption}
              fill
              className="object-cover"
              sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
              priority={false}
              quality={isMobile ? 75 : 85}
              placeholder="blur"
              blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
              loading={isMobile ? "eager" : "lazy"}
              onLoad={() => {
                setImageLoading(false);
                console.log(`Image loaded successfully: ${imageUrl}`);
              }}
              onError={(error) => {
                console.error(`Image failed to load: ${imageUrl}`, error);
                setImageError(true);
                setImageLoading(false);
              }}
            />
          </motion.div>
        )}
      </div>

      {/* Content */}
      <div className="p-4">
        {name && (
          <h3 className="font-semibold text-gray-900 mb-2 text-lg">{name}</h3>
        )}
        <p className="text-gray-700 text-sm leading-relaxed">{caption}</p>
        
        {/* Admin Controls */}
        {isAdmin && (onEdit || onDelete) && (
          <div className="mt-4 flex gap-2">
            {onEdit && (
              <motion.button
                onClick={() => onEdit(id)}
                className="px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Edit
              </motion.button>
            )}
            {onDelete && (
              <motion.button
                onClick={() => onDelete(id)}
                className="px-3 py-1 bg-red-500 text-white text-xs rounded hover:bg-red-600 transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Delete
              </motion.button>
            )}
          </div>
        )}
      </div>
    </motion.div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

interface ImageDebuggerProps {
  imageUrl: string;
  alt: string;
}

export default function ImageDebugger({ imageUrl, alt }: ImageDebuggerProps) {
  const [debugInfo, setDebugInfo] = useState({
    userAgent: '',
    viewport: { width: 0, height: 0 },
    devicePixelRatio: 1,
    connection: '',
    imageLoaded: false,
    imageError: null as string | null,
    loadTime: 0
  });

  useEffect(() => {
    const startTime = Date.now();
    
    setDebugInfo(prev => ({
      ...prev,
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      devicePixelRatio: window.devicePixelRatio || 1,
      connection: (navigator as any).connection?.effectiveType || 'unknown'
    }));

    const handleResize = () => {
      setDebugInfo(prev => ({
        ...prev,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        }
      }));
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleImageLoad = () => {
    const loadTime = Date.now() - (debugInfo.loadTime || Date.now());
    setDebugInfo(prev => ({
      ...prev,
      imageLoaded: true,
      loadTime
    }));
  };

  const handleImageError = (error: any) => {
    setDebugInfo(prev => ({
      ...prev,
      imageError: error.toString()
    }));
  };

  return (
    <div className="p-4 bg-gray-100 rounded-lg">
      <h3 className="font-bold mb-2">Image Debug Info</h3>
      
      <div className="mb-4">
        <div className="relative w-32 h-32 bg-gray-200 rounded">
          <Image
            src={imageUrl}
            alt={alt}
            fill
            className="object-cover rounded"
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        </div>
      </div>

      <div className="text-xs space-y-1">
        <div><strong>Viewport:</strong> {debugInfo.viewport.width}x{debugInfo.viewport.height}</div>
        <div><strong>Device Pixel Ratio:</strong> {debugInfo.devicePixelRatio}</div>
        <div><strong>Connection:</strong> {debugInfo.connection}</div>
        <div><strong>Image Loaded:</strong> {debugInfo.imageLoaded ? 'Yes' : 'No'}</div>
        {debugInfo.imageError && (
          <div><strong>Error:</strong> {debugInfo.imageError}</div>
        )}
        {debugInfo.loadTime > 0 && (
          <div><strong>Load Time:</strong> {debugInfo.loadTime}ms</div>
        )}
        <div><strong>Is Mobile:</strong> {debugInfo.viewport.width <= 768 ? 'Yes' : 'No'}</div>
        <div><strong>Image URL:</strong> <span className="break-all">{imageUrl}</span></div>
      </div>
    </div>
  );
}
